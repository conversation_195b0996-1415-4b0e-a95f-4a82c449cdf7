#ifndef FONT_5X7_DIGITS_H
#define FONT_5X7_DIGITS_H

#include <Arduino.h>

// 5x7 点阵数字字体 (0-9)
// 每列用 uint8_t 表示，最低7位有效 (bit0=顶部, bit6=底部)
// 格式: { col0, col1, col2, col3, col4 }

// ========== Normal 风格 ==========
const uint8_t digitFont_Normal[10][5] = {
    // 0: ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    { 0x3E, 0x41, 0x41, 0x41, 0x3E },

    // 1:   ▓
    //      ▓
    //      ▓
    //      ▓
    //      ▓
    //      ▓
    //      ▓
    { 0x00, 0x42, 0x7F, 0x40, 0x00 },

    // 2: ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    //    ▓
    //    ▓
    //    ▓▓▓▓▓
    { 0x42, 0x61, 0x51, 0x49, 0x46 },

    // 3: ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    { 0x21, 0x41, 0x45, 0x4B, 0x31 },

    // 4: ▓   ▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //        ▓
    { 0x18, 0x14, 0x12, 0x7F, 0x10 },

    // 5: ▓▓▓▓▓
    //    ▓
    //    ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    { 0x27, 0x45, 0x45, 0x45, 0x39 },

    // 6: ▓▓▓▓▓
    //    ▓
    //    ▓
    //    ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    { 0x3C, 0x4A, 0x49, 0x49, 0x30 },

    // 7: ▓▓▓▓▓
    //        ▓
    //        ▓
    //        ▓
    //        ▓
    //        ▓
    //        ▓
    { 0x01, 0x71, 0x09, 0x05, 0x03 },

    // 8: ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    { 0x36, 0x49, 0x49, 0x49, 0x36 },

    // 9: ▓▓▓▓▓
    //    ▓   ▓
    //    ▓   ▓
    //    ▓▓▓▓▓
    //        ▓
    //        ▓
    //    ▓▓▓▓▓
    { 0x06, 0x49, 0x49, 0x29, 0x1E }
};





// ========== Digital Square 风格 ==========
// 格式：5列×7行，每列用一个字节表示（bit0=顶部，bit6=底部）
const uint8_t digitFont_Square[10][7] = {
    // 0: 方形数字0 - 转换为列格式
    //    █████  <- 第1行：所有列都有bit0
    //    █   █  <- 第2行：第1,5列有bit1
    //    █   █  <- 第3行：第1,5列有bit2
    //    █   █  <- 第4行：第1,5列有bit3
    //    █   █  <- 第5行：第1,5列有bit4
    //    █   █  <- 第6行：第1,5列有bit5
    //    █████  <- 第7行：所有列都有bit6
    { 0x7F, 0x41, 0x41, 0x41, 0x7F, 0x00, 0x00 },

    // 1: 方形数字1 - 只有中间列
    //      █
    //      █
    //      █
    //      █
    //      █
    //      █
    //      █
    { 0x00, 0x00, 0x7F, 0x00, 0x00, 0x00, 0x00 },

    // 2: 方形数字2
    //    █████
    //        █
    //        █
    //    █████
    //    █
    //    █
    //    █████
    { 0x47, 0x49, 0x49, 0x49, 0x71, 0x00, 0x00 },

    // 3: 方形数字3
    //    █████
    //        █
    //        █
    //     ████
    //        █
    //        █
    //    █████
    { 0x22, 0x41, 0x49, 0x49, 0x36, 0x00, 0x00 },

    // 4: 方形数字4
    //    █   █
    //    █   █
    //    █   █
    //    █████
    //        █
    //        █
    //        █
    { 0x18, 0x14, 0x12, 0x7F, 0x10, 0x00, 0x00 },

    // 5: 方形数字5
    //    █████
    //    █
    //    █
    //    █████
    //        █
    //        █
    //    █████
    { 0x39, 0x45, 0x45, 0x45, 0x27, 0x00, 0x00 },

    // 6: 方形数字6
    //    █████
    //    █
    //    █
    //    █████
    //    █   █
    //    █   █
    //    █████
    { 0x3E, 0x49, 0x49, 0x49, 0x30, 0x00, 0x00 },

    // 7: 方形数字7
    //    █████
    //        █
    //        █
    //        █
    //        █
    //        █
    //        █
    { 0x01, 0x01, 0x01, 0x01, 0x7F, 0x00, 0x00 },

    // 8: 方形数字8
    //    █████
    //    █   █
    //    █   █
    //    █████
    //    █   █
    //    █   █
    //    █████
    { 0x36, 0x49, 0x49, 0x49, 0x36, 0x00, 0x00 },

    // 9: 方形数字9
    //    █████
    //    █   █
    //    █   █
    //    █████
    //        █
    //        █
    //    █████
    { 0x06, 0x49, 0x49, 0x49, 0x3E, 0x00, 0x00 }
};

// ========== Bold/Thick 粗体风格 ==========
// 格式：5列×7行，每列用一个字节表示，上下横排单排，左右竖排双排
const uint8_t digitFont_Bold[10][7] = {
    // 0: 粗体数字0 - 转换为列格式
    //    █████  <- 第1行：所有列都有bit0
    //    ██ ██  <- 第2行：第1,2,4,5列有bit1
    //    ██ ██  <- 第3行：第1,2,4,5列有bit2
    //    ██ ██  <- 第4行：第1,2,4,5列有bit3
    //    ██ ██  <- 第5行：第1,2,4,5列有bit4
    //    ██ ██  <- 第6行：第1,2,4,5列有bit5
    //    █████  <- 第7行：所有列都有bit6
    { 0x7F, 0x7B, 0x41, 0x7B, 0x7F, 0x00, 0x00 },

    // 1: 粗体数字1 - 双列粗体
    //     ██
    //    ███
    //    ███
    //     ██
    //     ██
    //     ██
    //     ██
    { 0x00, 0x06, 0x7F, 0x7F, 0x00, 0x00, 0x00 },

    // 2: 粗体数字2 - 粗体版本
    //    █████
    //       ██
    //       ██
    //    █████
    //    ██
    //    ██
    //    █████
    { 0x67, 0x6B, 0x59, 0x59, 0x73, 0x00, 0x00 },

    // 3: 粗体数字3 - 转换为列格式
    { 0x22, 0x63, 0x49, 0x49, 0x77, 0x00, 0x00 },

    // 4: 粗体数字4 - 转换为列格式
    { 0x18, 0x34, 0x32, 0x7F, 0x30, 0x00, 0x00 },

    // 5: 粗体数字5 - 转换为列格式
    { 0x39, 0x65, 0x45, 0x45, 0x67, 0x00, 0x00 },

    // 6: 粗体数字6 - 转换为列格式
    { 0x3E, 0x69, 0x49, 0x49, 0x70, 0x00, 0x00 },

    // 7: 粗体数字7 - 转换为列格式
    { 0x03, 0x03, 0x71, 0x0D, 0x03, 0x00, 0x00 },

    // 8: 粗体数字8 - 转换为列格式
    { 0x36, 0x6B, 0x49, 0x6B, 0x36, 0x00, 0x00 },

    // 9: 粗体数字9 - 转换为列格式
    { 0x0E, 0x49, 0x49, 0x4B, 0x3E, 0x00, 0x00 }
};

// 外部声明，供其他文件使用
extern const uint8_t digitFont_Normal[10][5];   // 默认圆形字体
extern const uint8_t digitFont_Square[10][7];   // 方形字体
extern const uint8_t digitFont_Bold[10][7];     // 粗体字体（方形+粗体）

#endif // FONT_5X7_DIGITS_H
