#include "VFD_Driver.h"

VFD_Driver::VFD_Driver() {
    brightness = 75;
    isInitialized = false;
}

bool VFD_Driver::init() {
    // 初始化引脚
    pinMode(VFD_CLK_PIN, OUTPUT);
    pinMode(VFD_DATA_PIN, OUTPUT);
    pinMode(VFD_CS_PIN, OUTPUT);

    // 初始状态
    digitalWrite(VFD_CLK_PIN, LOW);
    digitalWrite(VFD_DATA_PIN, LOW);
    digitalWrite(VFD_CS_PIN, HIGH);

    delay(100);

    // PT6302初始化序列
    // 设置显示位数
    chipSelect(true);
    sendByte(0xe0);
    delayMicroseconds(5);
    sendByte(0x0f);  // 16位显示
    chipSelect(false);
    delayMicroseconds(5);

    // 设置亮度
    chipSelect(true);
    sendByte(0xe4);
    delayMicroseconds(5);
    sendByte(0xff);  // 最大亮度
    chipSelect(false);
    delayMicroseconds(5);

    // 设置默认亮度
    setBrightness(75);

    // 清屏
    clear();

    isInitialized = true;
    return true;
}

void VFD_Driver::sendByte(uint8_t data) {
    for (int i = 0; i < 8; i++) {
        digitalWrite(VFD_DATA_PIN, (data >> i) & 0x01);
        delayMicroseconds(1);
        digitalWrite(VFD_CLK_PIN, HIGH);
        delayMicroseconds(1);
        digitalWrite(VFD_CLK_PIN, LOW);
        delayMicroseconds(1);
    }
}

void VFD_Driver::chipSelect(bool select) {
    digitalWrite(VFD_CS_PIN, select ? LOW : HIGH);
    delayMicroseconds(1);
}

void VFD_Driver::clear() {
    chipSelect(true);
    sendByte(0x20);  // 设置DDRAM地址为0
    for (int i = 0; i < VFD_DIGITS; i++) {
        sendByte(0x20);  // 发送空格字符
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::showDisplay() {
    chipSelect(true);
    sendByte(0xe8);  // 开显示命令
    chipSelect(false);
    delayMicroseconds(5);
}

void VFD_Driver::setBrightness(uint8_t level) {
    brightness = constrain(level, 0, 100);
    // PT6302亮度控制：0x00-0xFF
    uint8_t pt6302Level = map(brightness, 0, 100, 0, 255);

    chipSelect(true);
    sendByte(0xe4);  // 亮度设置命令
    sendByte(pt6302Level);
    chipSelect(false);
}

void VFD_Driver::writeOneChar(uint8_t position, char character) {
    chipSelect(true);
    sendByte(0x20 + position);
    sendByte(character);
    chipSelect(false);
}

void VFD_Driver::writeString(uint8_t position, const char* str) {
    chipSelect(true);
    sendByte(0x20 + position);
    while (*str && position < VFD_DIGITS) {
        sendByte(*str);
        str++;
        position++;
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::displayText(const char* text) {
    char displayBuffer[17];
    memset(displayBuffer, ' ', 16);
    displayBuffer[16] = '\0';
    
    size_t textLen = strlen(text);
    if (textLen > 16) textLen = 16;
    memcpy(displayBuffer, text, textLen);
    
    chipSelect(true);
    sendByte(0x20);
    for (int i = 0; i < 16; i++) {
        sendByte(displayBuffer[i]);
    }
    chipSelect(false);
    showDisplay();
}
