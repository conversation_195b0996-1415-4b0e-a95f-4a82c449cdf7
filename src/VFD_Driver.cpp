#include "VFD_Driver.h"
#include "font_5x7_digits.h"

VFD_Driver::VFD_Driver() {
    brightness = 75;
    isInitialized = false;
    for (int i = 0; i < 10; i++) digitToCGRAM[i] = -1;
    for (int i = 0; i < 8; i++) cgramContent[i] = -1;
}

bool VFD_Driver::init() {
    // 初始化引脚 - 基于原始ESP32C3.ino代码
    pinMode(VFD_CLK_PIN, OUTPUT);
    pinMode(VFD_DATA_PIN, OUTPUT);
    pinMode(VFD_CS_PIN, OUTPUT);

    // 初始状态
    digitalWrite(VFD_CLK_PIN, LOW);
    digitalWrite(VFD_DATA_PIN, LOW);
    digitalWrite(VFD_CS_PIN, HIGH);

    delay(100);

    // PT6302正确初始化序列 - 基于ESP8266原始代码
    // 设置显示位数
    chipSelect(true);
    sendByte(0xe0);
    delayMicroseconds(5);
    sendByte(0x0f);  // 16位显示
    chipSelect(false);
    delayMicroseconds(5);

    // 设置亮度
    chipSelect(true);
    sendByte(0xe4);
    delayMicroseconds(5);
    sendByte(0xff);  // 最大亮度
    chipSelect(false);
    delayMicroseconds(5);

    // 设置默认亮度
    setBrightness(75);

    // 清屏
    clear();

    isInitialized = true;
    return true;
}



void VFD_Driver::delay1() {
    delayMicroseconds(1);
}

void VFD_Driver::sendByte(uint8_t data) {
    for (int i = 0; i < 8; i++) {
        digitalWrite(VFD_DATA_PIN, (data >> i) & 0x01);
        delayMicroseconds(1);
        digitalWrite(VFD_CLK_PIN, HIGH);
        delayMicroseconds(1);
        digitalWrite(VFD_CLK_PIN, LOW);
        delayMicroseconds(1);
    }
}

void VFD_Driver::chipSelect(bool select) {
    digitalWrite(VFD_CS_PIN, select ? LOW : HIGH);
    delayMicroseconds(1);
}

void VFD_Driver::clear() {
    chipSelect(true);
    sendByte(0x20);  // 设置DDRAM地址为0
    for (int i = 0; i < VFD_DIGITS; i++) {
        sendByte(0x20);  // 发送空格字符
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::showDisplay() {
    // 使用ESP8266原始代码的显示命令
    chipSelect(true);
    sendByte(0xe8);  // 开显示命令
    chipSelect(false);
    delayMicroseconds(5);  // 添加延迟确保命令执行
}

void VFD_Driver::setBrightness(uint8_t level) {
    brightness = constrain(level, 0, 100);
    // PT6302亮度控制：0x00-0xFF
    uint8_t pt6302Level = map(brightness, 0, 100, 0, 255);

    chipSelect(true);
    sendByte(0xe4);  // 亮度设置命令
    sendByte(pt6302Level);
    chipSelect(false);
}

void VFD_Driver::writeOneChar(uint8_t position, char character) {
    chipSelect(true);
    sendByte(0x20 + position);

    // 根据ESP8266原始代码，数字需要特殊处理
    if (character >= '0' && character <= '9') {
        sendByte(character);  // 数字直接发送ASCII码
    } else {
        sendByte(character);  // 其他字符直接发送
    }

    chipSelect(false);
}

void VFD_Driver::writeString(uint8_t position, const char* str) {
    chipSelect(true);
    sendByte(0x20 + position);
    while (*str && position < VFD_DIGITS) {
        sendByte(*str);
        str++;
        position++;
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::displayText(const char* text) {
    char displayBuffer[17];
    memset(displayBuffer, ' ', 16);
    displayBuffer[16] = '\0';
    
    size_t textLen = strlen(text);
    if (textLen > 16) textLen = 16;
    memcpy(displayBuffer, text, textLen);
    
    chipSelect(true);
    sendByte(0x20);
    for (int i = 0; i < 16; i++) {
        sendByte(displayBuffer[i]);
    }
    chipSelect(false);
    showDisplay();
}

void VFD_Driver::writeCGRAM(uint8_t address, const uint8_t* fontData) {
    if (address > 7) return;

    // VFD CGRAM写入 - 5列×7行格式
    chipSelect(true);
    sendByte(0x40 + address);

    // 写入5列数据（每列7位）
    for (int i = 0; i < 5; i++) {
        sendByte(fontData[i]);
    }

    chipSelect(false);
}

void VFD_Driver::loadDynamicFont(const char* text) {
    bool needDigits[10] = {false};
    for (int i = 0; text[i] != '\0'; i++) {
        if (text[i] >= '0' && text[i] <= '9') {
            needDigits[text[i] - '0'] = true;
        }
    }

    int cgramPos = 0;
    for (int digit = 0; digit < 10 && cgramPos < 8; digit++) {
        if (needDigits[digit]) {
            writeCGRAM(cgramPos, digitFont_Square[digit]);
            digitToCGRAM[digit] = cgramPos;
            cgramContent[cgramPos] = digit;
            cgramPos++;
        }
    }
}

void VFD_Driver::loadDynamicFontBold(const char* text) {
    bool needDigits[10] = {false};
    for (int i = 0; text[i] != '\0'; i++) {
        if (text[i] >= '0' && text[i] <= '9') {
            needDigits[text[i] - '0'] = true;
        }
    }

    int cgramPos = 0;
    for (int digit = 0; digit < 10 && cgramPos < 8; digit++) {
        if (needDigits[digit]) {
            writeCGRAM(cgramPos, digitFont_Bold[digit]);
            digitToCGRAM[digit] = cgramPos;
            cgramContent[cgramPos] = digit;
            cgramPos++;
        }
    }
}

void VFD_Driver::displayTextWithCGRAM(const char* text) {
    chipSelect(true);
    sendByte(0x20);

    for (int i = 0; i < strlen(text) && i < 16; i++) {
        char c = text[i];
        uint8_t charCode;

        if (c >= '0' && c <= '9') {
            int digit = c - '0';
            int cgramPos = digitToCGRAM[digit];
            if (cgramPos >= 0) {
                charCode = cgramPos;
            } else {
                charCode = 0x30 + digit;
            }
        } else {
            charCode = (uint8_t)c;
        }

        sendByte(charCode);
    }

    chipSelect(false);
    showDisplay();
}

void VFD_Driver::displayTimeWithFontType(const char* timeStr, int fontType) {
    switch(fontType) {
        case 0:
            displayText(timeStr);
            break;
        case 1:
            loadDynamicFont(timeStr);
            displayTextWithCGRAM(timeStr);
            break;
        case 2:
            loadDynamicFontBold(timeStr);
            displayTextWithCGRAM(timeStr);
            break;
        default:
            displayText(timeStr);
            break;
    }
}




