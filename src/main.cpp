#include <Arduino.h>
#include <WiFi.h>
#include <WebServer.h>
#include <DNSServer.h>
#include <EEPROM.h>
#include "VFD_Driver.h"

VFD_Driver vfd;
WebServer server(80);
DNSServer dnsServer;

// 简化配置结构体
struct SimpleConfig {
    char ssid[32];
    char password[64];
    int brightness;
    bool isConfigured;
};

SimpleConfig config;
bool configMode = false;
unsigned long configStartTime = 0;

// 函数声明
void startConfigMode();
void handleRoot();
void handleSave();
void loadConfig();
void saveConfig();
void connectToWiFi();

void setup() {
    Serial.begin(115200);
    delay(500);

    Serial.println("=== Simple VFD Display ===");

    // 初始化EEPROM
    EEPROM.begin(512);

    // 加载配置
    loadConfig();

    // 初始化VFD
    if (vfd.init()) {
        Serial.println("VFD initialized!");
        vfd.clear();
        vfd.setBrightness(config.brightness);
        vfd.displayText("VFD READY");
        delay(1000);
    } else {
        Serial.println("VFD init failed!");
        return;
    }

    // 检查WiFi配置
    if (config.isConfigured && strlen(config.ssid) > 0) {
        Serial.println("WiFi configured, attempting connection...");
        vfd.displayText("CONNECTING...");
        
        connectToWiFi();
        
        if (WiFi.status() == WL_CONNECTED) {
            Serial.println("WiFi connected!");
            vfd.displayText("CONNECTED");
            delay(1000);
            configMode = false;
        } else {
            Serial.println("WiFi failed, starting config mode...");
            vfd.displayText("WIFI FAILED");
            delay(1000);
            startConfigMode();
        }
    } else {
        Serial.println("No WiFi config, starting config mode...");
        vfd.displayText("CONFIG MODE");
        delay(500);
        startConfigMode();
    }
}

void loop() {
    // 处理Web请求
    server.handleClient();

    if (configMode) {
        // 配置模式 - 处理DNS请求
        dnsServer.processNextRequest();
        
        // 配置超时检查 (5分钟)
        if (millis() - configStartTime > 300000) {
            Serial.println("Config timeout, restarting...");
            vfd.displayText("TIMEOUT RESTART");
            delay(2000);
            ESP.restart();
        }
    } else {
        // 正常模式 - 显示简单信息
        static unsigned long lastUpdate = 0;
        if (millis() - lastUpdate > 5000) {
            lastUpdate = millis();
            if (WiFi.status() == WL_CONNECTED) {
                vfd.displayText("WIFI CONNECTED");
            } else {
                vfd.displayText("WIFI DISCONNECTED");
            }
        }
    }

    delay(100);
}

// 启动配置模式
void startConfigMode() {
    configMode = true;
    configStartTime = millis();

    Serial.println("Starting AP mode...");
    vfd.displayText("STARTING AP...");

    // 启动AP模式
    WiFi.mode(WIFI_AP);
    WiFi.disconnect(true);
    delay(500);
    
    // 配置AP
    bool apResult = WiFi.softAP("VFD_Clock_AP", "12345678", 6, false, 4);
    
    if (apResult) {
        Serial.println("AP started successfully!");
    } else {
        Serial.println("AP start failed!");
    }
    
    delay(1000);

    // 启动DNS服务器
    dnsServer.start(53, "*", WiFi.softAPIP());

    Serial.println("AP Configuration:");
    Serial.println("SSID: VFD_Clock_AP");
    Serial.println("Password: 12345678");
    Serial.print("IP: ");
    Serial.println(WiFi.softAPIP());

    // 设置Web服务器路由
    server.on("/", handleRoot);
    server.on("/save", HTTP_POST, handleSave);
    server.onNotFound(handleRoot);
    server.begin();

    vfd.displayText("WIFI:VFD_Clock_AP");
    Serial.println("Connect to VFD_Clock_AP and go to ***********");
}

// 连接WiFi
void connectToWiFi() {
    WiFi.mode(WIFI_STA);
    WiFi.begin(config.ssid, config.password);
    
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    Serial.println();
}

// 加载配置
void loadConfig() {
    EEPROM.get(0, config);
    
    // 检查配置是否有效
    if (config.brightness < 0 || config.brightness > 100) {
        Serial.println("Invalid config, setting defaults...");
        
        // 设置默认值
        memset(&config, 0, sizeof(config));
        strcpy(config.ssid, "");
        strcpy(config.password, "");
        config.brightness = 75;
        config.isConfigured = false;
        
        saveConfig();
        Serial.println("Default config saved");
    }
    
    Serial.println("Config loaded:");
    Serial.println("SSID: " + String(config.ssid));
    Serial.println("Brightness: " + String(config.brightness));
}

// 保存配置
void saveConfig() {
    EEPROM.put(0, config);
    EEPROM.commit();
    Serial.println("Config saved");
}

// Web配置页面
void handleRoot() {
    String html = "<!DOCTYPE html><html><head>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
    html += "<title>VFD Display Config</title>";
    html += "<style>";
    html += "body{font-family:Arial,sans-serif;margin:20px;background:#f0f0f0}";
    html += ".container{background:white;padding:20px;border-radius:8px;max-width:400px;margin:0 auto}";
    html += "h1{color:#333;text-align:center}";
    html += ".form-group{margin-bottom:15px}";
    html += "label{display:block;margin-bottom:5px;font-weight:bold}";
    html += "input,select{width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;box-sizing:border-box}";
    html += "button{background:#007cba;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;width:100%}";
    html += "button:hover{background:#005a87}";
    html += "</style></head><body>";
    
    html += "<div class='container'>";
    html += "<h1>VFD Display Config</h1>";
    html += "<form action='/save' method='POST'>";
    
    html += "<div class='form-group'>";
    html += "<label for='ssid'>WiFi SSID:</label>";
    html += "<input type='text' name='ssid' id='ssid' value='";
    html += String(config.ssid);
    html += "' required>";
    html += "</div>";
    
    html += "<div class='form-group'>";
    html += "<label for='password'>WiFi Password:</label>";
    html += "<input type='password' name='password' id='password' value='";
    html += String(config.password);
    html += "' required>";
    html += "</div>";
    
    html += "<div class='form-group'>";
    html += "<label for='brightness'>Brightness (0-100):</label>";
    html += "<input type='number' name='brightness' id='brightness' min='0' max='100' value='";
    html += String(config.brightness);
    html += "' required>";
    html += "</div>";
    
    html += "<button type='submit'>Save Settings</button>";
    html += "</form>";
    html += "</div>";
    html += "</body></html>";
    
    server.send(200, "text/html", html);
}

// 处理保存
void handleSave() {
    String ssid = server.arg("ssid");
    String password = server.arg("password");
    int brightness = server.arg("brightness").toInt();
    
    // 保存配置
    ssid.toCharArray(config.ssid, sizeof(config.ssid));
    password.toCharArray(config.password, sizeof(config.password));
    config.brightness = constrain(brightness, 0, 100);
    config.isConfigured = true;
    
    saveConfig();
    
    Serial.println("Configuration saved:");
    Serial.println("SSID: " + String(config.ssid));
    Serial.println("Brightness: " + String(config.brightness));
    
    // 发送成功页面
    String html = "<!DOCTYPE html><html><head>";
    html += "<meta charset='UTF-8'>";
    html += "<title>Settings Saved</title>";
    html += "<style>body{font-family:Arial,sans-serif;text-align:center;margin:50px}</style>";
    html += "</head><body>";
    html += "<h1>Settings Saved!</h1>";
    html += "<p>Device will restart and connect to WiFi...</p>";
    html += "</body></html>";
    
    server.send(200, "text/html", html);
    delay(1000);
    
    // 重启设备
    ESP.restart();
}
