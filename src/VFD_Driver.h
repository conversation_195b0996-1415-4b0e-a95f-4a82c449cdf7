#ifndef VFD_DRIVER_H
#define VFD_DRIVER_H

#include <Arduino.h>

// VFD显示器参数
#define VFD_DIGITS 16
#define VFD_CS_PIN 8
#define VFD_DATA_PIN 5
#define VFD_CLK_PIN 3

class VFD_Driver {
private:
    uint8_t brightness;
    bool isInitialized;

    // 硬件控制
    void chipSelect(bool select);
    void sendByte(uint8_t data);

public:
    VFD_Driver();

    // 基础功能
    bool init();
    void clear();
    void setBrightness(uint8_t level);
    void writeOneChar(uint8_t position, char character);
    void writeString(uint8_t position, const char* str);
    void showDisplay();
    void displayText(const char* text);
};

#endif // VFD_DRIVER_H
